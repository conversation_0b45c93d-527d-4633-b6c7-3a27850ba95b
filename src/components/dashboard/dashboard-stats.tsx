import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Users, 
  UserPlus, 
  TrendingUp, 
  Target, 
  Award,
  Activity
} from "lucide-react";
import type { DashboardStats } from "@/lib/types";

interface DashboardStatsProps {
  stats: DashboardStats;
}

export function DashboardStatsCards({ stats }: DashboardStatsProps) {
  const statCards = [
    {
      title: "Total Leads",
      value: stats.totalLeads.toLocaleString(),
      description: "All time leads",
      icon: Users,
      color: "text-blue-600",
      bgColor: "bg-blue-50"
    },
    {
      title: "New Today",
      value: stats.newLeadsToday.toLocaleString(),
      description: "Leads added today",
      icon: UserPlus,
      color: "text-green-600",
      bgColor: "bg-green-50"
    },
    {
      title: "Hot Leads",
      value: stats.hotLeads.toLocaleString(),
      description: "High-quality prospects",
      icon: TrendingUp,
      color: "text-red-600",
      bgColor: "bg-red-50"
    },
    {
      title: "Conversion Rate",
      value: `${stats.conversionRate}%`,
      description: "Lead to customer rate",
      icon: Target,
      color: "text-purple-600",
      bgColor: "bg-purple-50"
    },
    {
      title: "Avg Lead Score",
      value: stats.avgLeadScore.toString(),
      description: "Average quality score",
      icon: Award,
      color: "text-orange-600",
      bgColor: "bg-orange-50"
    },
    {
      title: "Top Source",
      value: stats.topSource,
      description: "Best performing source",
      icon: Activity,
      color: "text-indigo-600",
      bgColor: "bg-indigo-50"
    }
  ];

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6">
      {statCards.map((stat, index) => {
        const Icon = stat.icon;
        return (
          <Card key={index} className="relative overflow-hidden">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              <div className={`p-2 rounded-full ${stat.bgColor}`}>
                <Icon className={`h-4 w-4 ${stat.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <p className="text-xs text-muted-foreground">
                {stat.description}
              </p>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}

interface QualityBreakdownProps {
  hotLeads: number;
  warmLeads: number;
  coldLeads: number;
}

export function LeadQualityBreakdown({ hotLeads, warmLeads, coldLeads }: QualityBreakdownProps) {
  const total = hotLeads + warmLeads + coldLeads;
  
  const qualities = [
    {
      label: "Hot",
      count: hotLeads,
      percentage: total > 0 ? Math.round((hotLeads / total) * 100) : 0,
      color: "bg-red-500",
      textColor: "text-red-700",
      bgColor: "bg-red-50"
    },
    {
      label: "Warm",
      count: warmLeads,
      percentage: total > 0 ? Math.round((warmLeads / total) * 100) : 0,
      color: "bg-yellow-500",
      textColor: "text-yellow-700",
      bgColor: "bg-yellow-50"
    },
    {
      label: "Cold",
      count: coldLeads,
      percentage: total > 0 ? Math.round((coldLeads / total) * 100) : 0,
      color: "bg-blue-500",
      textColor: "text-blue-700",
      bgColor: "bg-blue-50"
    }
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Lead Quality Distribution</CardTitle>
        <CardDescription>
          Breakdown of leads by quality score
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {qualities.map((quality) => (
          <div key={quality.label} className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className={`w-3 h-3 rounded-full ${quality.color}`} />
                <span className="text-sm font-medium">{quality.label} Leads</span>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="secondary" className={quality.bgColor}>
                  {quality.count}
                </Badge>
                <span className="text-sm text-muted-foreground">
                  {quality.percentage}%
                </span>
              </div>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className={`h-2 rounded-full ${quality.color}`}
                style={{ width: `${quality.percentage}%` }}
              />
            </div>
          </div>
        ))}
        
        <div className="pt-4 border-t">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">Total Leads</span>
            <Badge variant="outline" className="text-lg px-3 py-1">
              {total.toLocaleString()}
            </Badge>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
