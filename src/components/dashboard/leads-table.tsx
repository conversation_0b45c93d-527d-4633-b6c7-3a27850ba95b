"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { 
  Search, 
  Filter, 
  Mail, 
  Phone, 
  Building, 
  Calendar,
  Star,
  TrendingUp,
  TrendingDown,
  Minus
} from "lucide-react";
import type { Lead, LeadSource } from "@/lib/types";

interface LeadsTableProps {
  leads: Lead[];
  sources: LeadSource[];
  total: number;
  onFilterChange?: (filters: LeadFilters) => void;
  onPageChange?: (page: number) => void;
  currentPage?: number;
  pageSize?: number;
}

export interface LeadFilters {
  search?: string;
  quality?: 'hot' | 'warm' | 'cold' | '';
  status?: 'new' | 'contacted' | 'qualified' | 'converted' | 'lost' | '';
  sourceId?: string;
}

const qualityColors = {
  hot: "bg-red-100 text-red-800 border-red-200",
  warm: "bg-yellow-100 text-yellow-800 border-yellow-200",
  cold: "bg-blue-100 text-blue-800 border-blue-200",
} as const;

const statusColors = {
  new: "bg-gray-100 text-gray-800 border-gray-200",
  contacted: "bg-blue-100 text-blue-800 border-blue-200",
  qualified: "bg-green-100 text-green-800 border-green-200",
  converted: "bg-emerald-100 text-emerald-800 border-emerald-200",
  lost: "bg-red-100 text-red-800 border-red-200",
} as const;

function getInitials(firstName?: string, lastName?: string, email?: string): string {
  if (firstName && lastName) {
    return `${firstName[0]}${lastName[0]}`.toUpperCase();
  }
  if (firstName) {
    return firstName.slice(0, 2).toUpperCase();
  }
  if (email) {
    return email.slice(0, 2).toUpperCase();
  }
  return 'U';
}

function getLeadScoreIcon(score: number) {
  if (score >= 80) return <TrendingUp className="h-4 w-4 text-green-600" />;
  if (score >= 50) return <Minus className="h-4 w-4 text-yellow-600" />;
  return <TrendingDown className="h-4 w-4 text-red-600" />;
}

function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  });
}

export function LeadsTable({ 
  leads, 
  sources, 
  total, 
  onFilterChange,
  onPageChange,
  currentPage = 1,
  pageSize = 50
}: LeadsTableProps) {
  const [filters, setFilters] = useState<LeadFilters>({});

  const handleFilterChange = (newFilters: Partial<LeadFilters>) => {
    const updatedFilters = { ...filters, ...newFilters };
    setFilters(updatedFilters);
    onFilterChange?.(updatedFilters);
  };

  const totalPages = Math.ceil(total / pageSize);

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Leads</CardTitle>
            <CardDescription>
              Manage and track your leads ({total.toLocaleString()} total)
            </CardDescription>
          </div>
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search leads by name, email, or company..."
              value={filters.search || ''}
              onChange={(e) => handleFilterChange({ search: e.target.value })}
              className="pl-10"
            />
          </div>
          
          <Select
            value={filters.quality || ''}
            onValueChange={(value) => handleFilterChange({ quality: value as any })}
          >
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="Quality" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All Quality</SelectItem>
              <SelectItem value="hot">Hot</SelectItem>
              <SelectItem value="warm">Warm</SelectItem>
              <SelectItem value="cold">Cold</SelectItem>
            </SelectContent>
          </Select>

          <Select
            value={filters.status || ''}
            onValueChange={(value) => handleFilterChange({ status: value as any })}
          >
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All Status</SelectItem>
              <SelectItem value="new">New</SelectItem>
              <SelectItem value="contacted">Contacted</SelectItem>
              <SelectItem value="qualified">Qualified</SelectItem>
              <SelectItem value="converted">Converted</SelectItem>
              <SelectItem value="lost">Lost</SelectItem>
            </SelectContent>
          </Select>

          <Select
            value={filters.sourceId || ''}
            onValueChange={(value) => handleFilterChange({ sourceId: value })}
          >
            <SelectTrigger className="w-[160px]">
              <SelectValue placeholder="Source" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All Sources</SelectItem>
              {sources.map((source) => (
                <SelectItem key={source.id} value={source.id}>
                  {source.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Table */}
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Lead</TableHead>
                <TableHead>Contact</TableHead>
                <TableHead>Company</TableHead>
                <TableHead>Quality</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Score</TableHead>
                <TableHead>Created</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {leads.map((lead) => {
                const sourceName = sources.find(s => s.id === lead.leadSourceId)?.name || 'Unknown';
                
                return (
                  <TableRow key={lead.id} className="hover:bg-muted/50">
                    <TableCell>
                      <div className="flex items-center space-x-3">
                        <Avatar className="h-8 w-8">
                          <AvatarFallback className="text-xs">
                            {getInitials(lead.firstName, lead.lastName, lead.email)}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">
                            {lead.firstName && lead.lastName 
                              ? `${lead.firstName} ${lead.lastName}`
                              : lead.email
                            }
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {sourceName}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <div className="space-y-1">
                        <div className="flex items-center text-sm">
                          <Mail className="h-3 w-3 mr-1 text-muted-foreground" />
                          {lead.email}
                        </div>
                        {lead.phone && (
                          <div className="flex items-center text-sm text-muted-foreground">
                            <Phone className="h-3 w-3 mr-1" />
                            {lead.phone}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      {lead.company ? (
                        <div className="flex items-center text-sm">
                          <Building className="h-3 w-3 mr-1 text-muted-foreground" />
                          <div>
                            <div>{lead.company}</div>
                            {lead.jobTitle && (
                              <div className="text-xs text-muted-foreground">
                                {lead.jobTitle}
                              </div>
                            )}
                          </div>
                        </div>
                      ) : (
                        <span className="text-muted-foreground text-sm">-</span>
                      )}
                    </TableCell>
                    
                    <TableCell>
                      <Badge 
                        variant="outline" 
                        className={qualityColors[lead.leadQuality]}
                      >
                        {lead.leadQuality}
                      </Badge>
                    </TableCell>
                    
                    <TableCell>
                      <Badge 
                        variant="outline" 
                        className={statusColors[lead.leadStatus]}
                      >
                        {lead.leadStatus}
                      </Badge>
                    </TableCell>
                    
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        {getLeadScoreIcon(lead.leadScore)}
                        <span className="font-medium">{lead.leadScore}</span>
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <div className="flex items-center text-sm text-muted-foreground">
                        <Calendar className="h-3 w-3 mr-1" />
                        {formatDate(lead.createdAt)}
                      </div>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between mt-4">
            <div className="text-sm text-muted-foreground">
              Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, total)} of {total} leads
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onPageChange?.(currentPage - 1)}
                disabled={currentPage <= 1}
              >
                Previous
              </Button>
              <div className="flex items-center space-x-1">
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  const page = i + 1;
                  return (
                    <Button
                      key={page}
                      variant={currentPage === page ? "default" : "outline"}
                      size="sm"
                      onClick={() => onPageChange?.(page)}
                      className="w-8 h-8 p-0"
                    >
                      {page}
                    </Button>
                  );
                })}
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onPageChange?.(currentPage + 1)}
                disabled={currentPage >= totalPages}
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
