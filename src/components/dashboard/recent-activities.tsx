import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { 
  Mail, 
  Phone, 
  MousePointer, 
  Calendar, 
  FileText,
  MessageSquare,
  Download,
  ExternalLink
} from "lucide-react";
import type { RecentActivity } from "@/lib/types";

interface RecentActivitiesProps {
  activities: RecentActivity[];
}

const activityIcons = {
  form_submit: FileText,
  email_open: Mail,
  link_click: MousePointer,
  call: Phone,
  meeting: Calendar,
  chat: MessageSquare,
  download: Download,
  page_view: ExternalLink,
} as const;

const activityColors = {
  form_submit: "text-blue-600 bg-blue-50",
  email_open: "text-green-600 bg-green-50",
  link_click: "text-purple-600 bg-purple-50",
  call: "text-orange-600 bg-orange-50",
  meeting: "text-indigo-600 bg-indigo-50",
  chat: "text-pink-600 bg-pink-50",
  download: "text-yellow-600 bg-yellow-50",
  page_view: "text-gray-600 bg-gray-50",
} as const;

const qualityColors = {
  hot: "bg-red-100 text-red-800 border-red-200",
  warm: "bg-yellow-100 text-yellow-800 border-yellow-200",
  cold: "bg-blue-100 text-blue-800 border-blue-200",
} as const;

function getActivityIcon(activityType: string) {
  const IconComponent = activityIcons[activityType as keyof typeof activityIcons] || FileText;
  return IconComponent;
}

function getActivityColor(activityType: string) {
  return activityColors[activityType as keyof typeof activityColors] || "text-gray-600 bg-gray-50";
}

function formatActivityType(activityType: string): string {
  return activityType
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

function getInitials(name?: string, email?: string): string {
  if (name) {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  }
  
  if (email) {
    return email.slice(0, 2).toUpperCase();
  }
  
  return 'U';
}

function formatTimeAgo(dateString: string): string {
  const now = new Date();
  const date = new Date(dateString);
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
  
  if (diffInSeconds < 60) {
    return 'Just now';
  }
  
  const diffInMinutes = Math.floor(diffInSeconds / 60);
  if (diffInMinutes < 60) {
    return `${diffInMinutes}m ago`;
  }
  
  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) {
    return `${diffInHours}h ago`;
  }
  
  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 7) {
    return `${diffInDays}d ago`;
  }
  
  return date.toLocaleDateString();
}

export function RecentActivities({ activities }: RecentActivitiesProps) {
  if (activities.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Recent Activities</CardTitle>
          <CardDescription>
            Latest lead interactions and activities
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No recent activities found</p>
            <p className="text-sm">Activities will appear here as leads interact with your content</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Recent Activities</CardTitle>
        <CardDescription>
          Latest lead interactions and activities
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {activities.map((activity) => {
            const Icon = getActivityIcon(activity.activityType);
            const colorClasses = getActivityColor(activity.activityType);
            
            return (
              <div key={activity.id} className="flex items-start space-x-4 p-3 rounded-lg border bg-card hover:bg-accent/50 transition-colors">
                <div className={`p-2 rounded-full ${colorClasses}`}>
                  <Icon className="h-4 w-4" />
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-1">
                    <div className="flex items-center space-x-2">
                      <Avatar className="h-6 w-6">
                        <AvatarFallback className="text-xs">
                          {getInitials(activity.leadName, activity.leadEmail)}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex items-center space-x-2">
                        <span className="text-sm font-medium truncate">
                          {activity.leadName || activity.leadEmail}
                        </span>
                        <Badge 
                          variant="outline" 
                          className={`text-xs ${qualityColors[activity.leadQuality]}`}
                        >
                          {activity.leadQuality}
                        </Badge>
                      </div>
                    </div>
                    <span className="text-xs text-muted-foreground whitespace-nowrap">
                      {formatTimeAgo(activity.createdAt)}
                    </span>
                  </div>
                  
                  <div className="space-y-1">
                    <p className="text-sm">
                      <span className="font-medium">
                        {formatActivityType(activity.activityType)}
                      </span>
                      {activity.description && (
                        <span className="text-muted-foreground ml-1">
                          - {activity.description}
                        </span>
                      )}
                    </p>
                    
                    {activity.leadEmail !== activity.leadName && (
                      <p className="text-xs text-muted-foreground">
                        {activity.leadEmail}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
        
        {activities.length >= 10 && (
          <div className="mt-4 pt-4 border-t text-center">
            <button className="text-sm text-muted-foreground hover:text-foreground transition-colors">
              View all activities →
            </button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Activity Summary Component
interface ActivitySummaryProps {
  activities: RecentActivity[];
}

export function ActivitySummary({ activities }: ActivitySummaryProps) {
  const activityCounts = activities.reduce((acc, activity) => {
    acc[activity.activityType] = (acc[activity.activityType] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const topActivities = Object.entries(activityCounts)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 5);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Activity Summary</CardTitle>
        <CardDescription>
          Most common lead activities today
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {topActivities.map(([activityType, count]) => {
            const Icon = getActivityIcon(activityType);
            const colorClasses = getActivityColor(activityType);
            
            return (
              <div key={activityType} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`p-1.5 rounded-full ${colorClasses}`}>
                    <Icon className="h-3 w-3" />
                  </div>
                  <span className="text-sm font-medium">
                    {formatActivityType(activityType)}
                  </span>
                </div>
                <Badge variant="secondary">
                  {count}
                </Badge>
              </div>
            );
          })}
          
          {topActivities.length === 0 && (
            <div className="text-center py-4 text-muted-foreground">
              <p className="text-sm">No activities recorded today</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
