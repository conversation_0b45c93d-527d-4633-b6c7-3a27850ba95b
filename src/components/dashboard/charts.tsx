"use client";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  ChartContainer, 
  ChartTooltip, 
  ChartTooltipContent,
  ChartLegend,
  ChartLegendContent
} from "@/components/ui/chart";
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer
} from "recharts";
import type { 
  LeadTrendData, 
  SourceDistribution, 
  ConversionFunnelData,
  ChartDataPoint 
} from "@/lib/database/analytics";

// Lead Trends Line Chart
interface LeadTrendsChartProps {
  data: LeadTrendData[];
}

export function LeadTrendsChart({ data }: LeadTrendsChartProps) {
  const chartConfig = {
    total: {
      label: "Total Leads",
      color: "hsl(var(--chart-1))",
    },
    hot: {
      label: "Hot Leads",
      color: "hsl(var(--chart-2))",
    },
    warm: {
      label: "Warm Leads", 
      color: "hsl(var(--chart-3))",
    },
    cold: {
      label: "Cold Leads",
      color: "hsl(var(--chart-4))",
    },
    converted: {
      label: "Converted",
      color: "hsl(var(--chart-5))",
    },
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Lead Trends</CardTitle>
        <CardDescription>
          Daily lead generation and quality trends over time
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig}>
          <LineChart data={data}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis 
              dataKey="date" 
              tickFormatter={(value) => new Date(value).toLocaleDateString()}
            />
            <YAxis />
            <ChartTooltip 
              content={<ChartTooltipContent />}
              labelFormatter={(value) => new Date(value).toLocaleDateString()}
            />
            <ChartLegend content={<ChartLegendContent />} />
            <Line 
              type="monotone" 
              dataKey="total" 
              stroke="var(--color-total)" 
              strokeWidth={2}
              dot={{ r: 4 }}
            />
            <Line 
              type="monotone" 
              dataKey="hot" 
              stroke="var(--color-hot)" 
              strokeWidth={2}
              dot={{ r: 3 }}
            />
            <Line 
              type="monotone" 
              dataKey="warm" 
              stroke="var(--color-warm)" 
              strokeWidth={2}
              dot={{ r: 3 }}
            />
            <Line 
              type="monotone" 
              dataKey="cold" 
              stroke="var(--color-cold)" 
              strokeWidth={2}
              dot={{ r: 3 }}
            />
            <Line 
              type="monotone" 
              dataKey="converted" 
              stroke="var(--color-converted)" 
              strokeWidth={2}
              dot={{ r: 3 }}
            />
          </LineChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}

// Source Distribution Pie Chart
interface SourceDistributionChartProps {
  data: SourceDistribution[];
}

export function SourceDistributionChart({ data }: SourceDistributionChartProps) {
  const chartConfig = data.reduce((config, item, index) => {
    config[item.name.toLowerCase().replace(/\s+/g, '_')] = {
      label: item.name,
      color: item.color || `hsl(var(--chart-${(index % 5) + 1}))`,
    };
    return config;
  }, {} as Record<string, { label: string; color: string }>);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Lead Sources</CardTitle>
        <CardDescription>
          Distribution of leads by source
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig}>
          <PieChart>
            <ChartTooltip 
              content={<ChartTooltipContent />}
              formatter={(value, name) => [
                `${value} leads (${data.find(d => d.name === name)?.percentage}%)`,
                name
              ]}
            />
            <Pie
              data={data}
              dataKey="value"
              nameKey="name"
              cx="50%"
              cy="50%"
              outerRadius={80}
              label={({ name, percentage }) => `${name}: ${percentage}%`}
            >
              {data.map((entry, index) => (
                <Cell 
                  key={`cell-${index}`} 
                  fill={entry.color || `hsl(var(--chart-${(index % 5) + 1}))`} 
                />
              ))}
            </Pie>
          </PieChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}

// Conversion Funnel Chart
interface ConversionFunnelChartProps {
  data: ConversionFunnelData[];
}

export function ConversionFunnelChart({ data }: ConversionFunnelChartProps) {
  const chartConfig = data.reduce((config, item, index) => {
    config[item.stage.toLowerCase().replace(/\s+/g, '_')] = {
      label: item.stage,
      color: item.color,
    };
    return config;
  }, {} as Record<string, { label: string; color: string }>);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Conversion Funnel</CardTitle>
        <CardDescription>
          Lead progression through sales stages
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig}>
          <BarChart data={data} layout="horizontal">
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis type="number" />
            <YAxis dataKey="stage" type="category" width={100} />
            <ChartTooltip 
              content={<ChartTooltipContent />}
              formatter={(value, name) => [
                `${value} leads (${data.find(d => d.stage === name)?.percentage}%)`,
                name
              ]}
            />
            <Bar dataKey="count" radius={[0, 4, 4, 0]}>
              {data.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
            </Bar>
          </BarChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}

// Lead Score Distribution Chart
interface LeadScoreChartProps {
  data: ChartDataPoint[];
}

export function LeadScoreChart({ data }: LeadScoreChartProps) {
  const chartConfig = {
    value: {
      label: "Number of Leads",
      color: "hsl(var(--chart-1))",
    },
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Lead Score Distribution</CardTitle>
        <CardDescription>
          Distribution of leads by score ranges
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig}>
          <BarChart data={data}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="date" />
            <YAxis />
            <ChartTooltip 
              content={<ChartTooltipContent />}
              labelFormatter={(label) => `Score Range: ${label}`}
            />
            <Bar 
              dataKey="value" 
              fill="var(--color-value)"
              radius={[4, 4, 0, 0]}
            />
          </BarChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}

// Area Chart for Quality Trends
interface QualityTrendsChartProps {
  data: ChartDataPoint[];
}

export function QualityTrendsChart({ data }: QualityTrendsChartProps) {
  // Transform data for stacked area chart
  const transformedData = data.reduce((acc, item) => {
    const existing = acc.find(d => d.date === item.date);
    if (existing) {
      existing[item.category!] = item.value;
    } else {
      acc.push({
        date: item.date,
        [item.category!]: item.value,
        hot: item.category === 'hot' ? item.value : 0,
        warm: item.category === 'warm' ? item.value : 0,
        cold: item.category === 'cold' ? item.value : 0,
      });
    }
    return acc;
  }, [] as any[]);

  const chartConfig = {
    hot: {
      label: "Hot Leads",
      color: "hsl(var(--chart-2))",
    },
    warm: {
      label: "Warm Leads",
      color: "hsl(var(--chart-3))",
    },
    cold: {
      label: "Cold Leads",
      color: "hsl(var(--chart-4))",
    },
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Lead Quality Trends</CardTitle>
        <CardDescription>
          Lead quality distribution over time
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig}>
          <AreaChart data={transformedData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis 
              dataKey="date" 
              tickFormatter={(value) => new Date(value).toLocaleDateString()}
            />
            <YAxis />
            <ChartTooltip 
              content={<ChartTooltipContent />}
              labelFormatter={(value) => new Date(value).toLocaleDateString()}
            />
            <ChartLegend content={<ChartLegendContent />} />
            <Area
              type="monotone"
              dataKey="hot"
              stackId="1"
              stroke="var(--color-hot)"
              fill="var(--color-hot)"
              fillOpacity={0.6}
            />
            <Area
              type="monotone"
              dataKey="warm"
              stackId="1"
              stroke="var(--color-warm)"
              fill="var(--color-warm)"
              fillOpacity={0.6}
            />
            <Area
              type="monotone"
              dataKey="cold"
              stackId="1"
              stroke="var(--color-cold)"
              fill="var(--color-cold)"
              fillOpacity={0.6}
            />
          </AreaChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}
