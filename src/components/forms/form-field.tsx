import React from "react";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { AlertCircle } from "lucide-react";

interface FormFieldProps {
  label: string;
  name: string;
  type?: string;
  placeholder: string;
  required?: boolean;
  disabled?: boolean;
  errors?: string[];
  isTextarea?: boolean;
  rows?: number;
  icon?: React.ReactNode;
  description?: string;
  className?: string;
}

export function FormField({
  label,
  name,
  type = "text",
  placeholder,
  required = false,
  disabled = false,
  errors = [],
  isTextarea = false,
  rows = 4,
  icon,
  description,
  className = "",
}: FormFieldProps) {
  const hasErrors = errors.length > 0;
  const fieldId = `field-${name}`;

  return (
    <div className={`space-y-2 ${className}`}>
      <Label htmlFor={fieldId} className="text-sm font-medium flex items-center gap-2">
        {icon}
        {label} {required && <span className="text-red-500">*</span>}
      </Label>

      {description && (
        <p className="text-sm text-muted-foreground">{description}</p>
      )}

      {isTextarea ? (
        <Textarea
          id={fieldId}
          name={name}
          placeholder={placeholder}
          required={required}
          disabled={disabled}
          rows={rows}
          className={hasErrors ? "border-red-500 focus:border-red-500" : ""}
          aria-describedby={hasErrors ? `${fieldId}-error` : undefined}
        />
      ) : (
        <div className="relative">
          <Input
            id={fieldId}
            type={type}
            name={name}
            placeholder={placeholder}
            required={required}
            disabled={disabled}
            className={`${hasErrors ? "border-red-500 focus:border-red-500" : ""} ${
              icon ? "pl-10" : ""
            }`}
            aria-describedby={hasErrors ? `${fieldId}-error` : undefined}
          />
          {icon && (
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">
              {icon}
            </div>
          )}
        </div>
      )}

      {hasErrors && (
        <div id={`${fieldId}-error`} className="space-y-1">
          {errors.map((error, index) => (
            <p
              key={index}
              className="text-sm text-red-600 flex items-center gap-1"
            >
              <AlertCircle className="h-3 w-3" />
              {error}
            </p>
          ))}
        </div>
      )}
    </div>
  );
}
