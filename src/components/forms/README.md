# Form Components

This directory contains reusable form components built with TypeScript, Zod validation, and Next.js server actions.

## Components

### FormField
A reusable form field component with built-in validation display, icons, and error handling.

**Props:**
- `label`: Field label
- `name`: Field name (used for form data)
- `type`: Input type (text, email, tel, url, etc.)
- `placeholder`: Placeholder text
- `required`: Whether the field is required
- `disabled`: Whether the field is disabled
- `errors`: Array of error messages to display
- `isTextarea`: Whether to render as textarea
- `rows`: Number of rows for textarea
- `icon`: Icon to display in the field
- `description`: Help text below the label

### LeadForm
A comprehensive lead generation form optimized for capturing qualified leads.

**Props:**
- `title`: Form title
- `description`: Form description
- `leadMagnet`: Lead magnet identifier
- `interests`: Array of interest options
- `className`: Additional CSS classes

**Features:**
- Required fields: First Name, Last Name, Email, Company
- Optional fields: Job Title, Phone, Website
- Customizable interest checkboxes
- Lead magnet tracking
- Automatic form reset on success

### NewsletterForm
A simple newsletter signup form with optional personalization.

**Props:**
- `title`: Form title
- `description`: Form description
- `interests`: Array of interest options
- `className`: Additional CSS classes
- `variant`: "card" or "inline" layout

**Features:**
- Required field: Email only
- Optional fields: First Name, Interests
- Two layout variants
- Privacy-focused messaging

## Server Actions

All forms use server actions defined in `src/actions/form.ts`:

- `submitContactForm`: Handles contact form submissions
- `submitLeadForm`: Handles lead generation form submissions
- `submitNewsletterForm`: Handles newsletter signups
- `submitChatLead`: Handles chat widget lead capture

## Validation

All forms use Zod schemas defined in `src/lib/types.ts`:

- `contactFormSchema`: Contact form validation
- `leadFormSchema`: Lead form validation
- `newsletterSchema`: Newsletter form validation

## Usage Examples

### Basic Contact Form
```tsx
import { FormField } from "@/components/forms";

<FormField
  label="Email"
  name="email"
  type="email"
  placeholder="<EMAIL>"
  required
  icon={<Mail className="h-4 w-4" />}
  errors={errors?.email}
/>
```

### Lead Generation Form
```tsx
import { LeadForm } from "@/components/forms";

<LeadForm 
  title="Download Free Guide"
  leadMagnet="marketing-guide-2024"
  interests={["Marketing", "Sales", "Product"]}
/>
```

### Newsletter Signup
```tsx
import { NewsletterForm } from "@/components/forms";

<NewsletterForm 
  variant="inline"
  title="Stay Updated"
  interests={["Weekly Newsletter", "Product Updates"]}
/>
```

## Integration

Forms automatically integrate with:
- N8N webhooks for lead processing
- UTM parameter tracking
- Client metadata collection (IP, user agent, etc.)
- Lead scoring and qualification
- Toast notifications for user feedback

## Styling

All components use Tailwind CSS and shadcn/ui components for consistent styling and theming.
