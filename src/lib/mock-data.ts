// Mock data for testing the dashboard
import type { 
  DashboardStats, 
  LeadsBySource, 
  RecentActivity, 
  Lead, 
  LeadSource 
} from "@/lib/types";
import type { 
  LeadTrendData, 
  SourceDistribution, 
  ConversionFunnelData,
  ChartDataPoint 
} from "@/lib/database/analytics";

export const mockDashboardStats: DashboardStats = {
  totalLeads: 1247,
  newLeadsToday: 23,
  hotLeads: 156,
  warmLeads: 342,
  coldLeads: 749,
  conversionRate: 12.5,
  avgLeadScore: 67.8,
  topSource: "Website Contact Form"
};

export const mockLeadTrends: LeadTrendData[] = [
  { date: "2024-01-01", total: 45, hot: 9, warm: 14, cold: 22, converted: 3 },
  { date: "2024-01-02", total: 52, hot: 11, warm: 16, cold: 25, converted: 4 },
  { date: "2024-01-03", total: 38, hot: 8, warm: 12, cold: 18, converted: 2 },
  { date: "2024-01-04", total: 61, hot: 13, warm: 19, cold: 29, converted: 5 },
  { date: "2024-01-05", total: 47, hot: 10, warm: 15, cold: 22, converted: 3 },
  { date: "2024-01-06", total: 55, hot: 12, warm: 17, cold: 26, converted: 4 },
  { date: "2024-01-07", total: 43, hot: 9, warm: 13, cold: 21, converted: 3 },
];

export const mockSourceDistribution: SourceDistribution[] = [
  { name: "Website Contact Form", value: 456, percentage: 37, color: "#8884d8" },
  { name: "Social Media", value: 298, percentage: 24, color: "#82ca9d" },
  { name: "Email Campaign", value: 234, percentage: 19, color: "#ffc658" },
  { name: "Referral", value: 156, percentage: 12, color: "#ff7300" },
  { name: "Direct", value: 103, percentage: 8, color: "#00ff00" },
];

export const mockConversionFunnel: ConversionFunnelData[] = [
  { stage: "New Leads", count: 1247, percentage: 100, color: "#8884d8" },
  { stage: "Contacted", count: 623, percentage: 50, color: "#82ca9d" },
  { stage: "Qualified", count: 312, percentage: 25, color: "#ffc658" },
  { stage: "Converted", count: 156, percentage: 12, color: "#00ff00" },
  { stage: "Lost", count: 187, percentage: 15, color: "#ff7300" },
];

export const mockLeadScoreDistribution: ChartDataPoint[] = [
  { date: "0-20", value: 89, label: "Score 0-20" },
  { date: "21-40", value: 156, label: "Score 21-40" },
  { date: "41-60", value: 298, label: "Score 41-60" },
  { date: "61-80", value: 423, label: "Score 61-80" },
  { date: "81-100", value: 281, label: "Score 81-100" },
];

export const mockQualityTrends: ChartDataPoint[] = [
  { date: "2024-01-01", value: 9, category: "hot", label: "Hot Leads" },
  { date: "2024-01-01", value: 14, category: "warm", label: "Warm Leads" },
  { date: "2024-01-01", value: 22, category: "cold", label: "Cold Leads" },
  { date: "2024-01-02", value: 11, category: "hot", label: "Hot Leads" },
  { date: "2024-01-02", value: 16, category: "warm", label: "Warm Leads" },
  { date: "2024-01-02", value: 25, category: "cold", label: "Cold Leads" },
  { date: "2024-01-03", value: 8, category: "hot", label: "Hot Leads" },
  { date: "2024-01-03", value: 12, category: "warm", label: "Warm Leads" },
  { date: "2024-01-03", value: 18, category: "cold", label: "Cold Leads" },
];

export const mockRecentActivities: RecentActivity[] = [
  {
    id: "1",
    leadId: "lead-1",
    leadEmail: "<EMAIL>",
    leadName: "John Doe",
    activityType: "form_submit",
    description: "Submitted contact form on homepage",
    createdAt: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago
    leadQuality: "hot"
  },
  {
    id: "2",
    leadId: "lead-2",
    leadEmail: "<EMAIL>",
    leadName: "Jane Smith",
    activityType: "email_open",
    description: "Opened welcome email",
    createdAt: new Date(Date.now() - 1000 * 60 * 60).toISOString(), // 1 hour ago
    leadQuality: "warm"
  },
  {
    id: "3",
    leadId: "lead-3",
    leadEmail: "<EMAIL>",
    leadName: "Mike Johnson",
    activityType: "link_click",
    description: "Clicked pricing page link",
    createdAt: new Date(Date.now() - 1000 * 60 * 90).toISOString(), // 1.5 hours ago
    leadQuality: "hot"
  },
  {
    id: "4",
    leadId: "lead-4",
    leadEmail: "<EMAIL>",
    leadName: "Sarah Wilson",
    activityType: "call",
    description: "Initial discovery call completed",
    createdAt: new Date(Date.now() - 1000 * 60 * 120).toISOString(), // 2 hours ago
    leadQuality: "warm"
  },
  {
    id: "5",
    leadId: "lead-5",
    leadEmail: "<EMAIL>",
    leadName: "David Brown",
    activityType: "meeting",
    description: "Demo meeting scheduled",
    createdAt: new Date(Date.now() - 1000 * 60 * 180).toISOString(), // 3 hours ago
    leadQuality: "cold"
  },
];

export const mockLeads: Lead[] = [
  {
    id: "lead-1",
    email: "<EMAIL>",
    firstName: "John",
    lastName: "Doe",
    phone: "******-0123",
    company: "Tech Corp",
    jobTitle: "Software Engineer",
    leadSourceId: "source-1",
    leadScore: 85,
    leadQuality: "hot",
    leadStatus: "new",
    isVerified: true,
    isDuplicate: false,
    isDeleted: false,
    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(), // 1 day ago
    updatedAt: new Date(Date.now() - 1000 * 60 * 60 * 12).toISOString(), // 12 hours ago
  },
  {
    id: "lead-2",
    email: "<EMAIL>",
    firstName: "Jane",
    lastName: "Smith",
    phone: "******-0124",
    company: "Startup Inc",
    jobTitle: "CTO",
    leadSourceId: "source-2",
    leadScore: 72,
    leadQuality: "warm",
    leadStatus: "contacted",
    isVerified: true,
    isDuplicate: false,
    isDeleted: false,
    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 48).toISOString(), // 2 days ago
    updatedAt: new Date(Date.now() - 1000 * 60 * 60 * 6).toISOString(), // 6 hours ago
  },
  {
    id: "lead-3",
    email: "<EMAIL>",
    firstName: "Mike",
    lastName: "Johnson",
    phone: "******-0125",
    company: "Big Corp",
    jobTitle: "VP Engineering",
    leadSourceId: "source-1",
    leadScore: 91,
    leadQuality: "hot",
    leadStatus: "qualified",
    isVerified: true,
    isDuplicate: false,
    isDeleted: false,
    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 72).toISOString(), // 3 days ago
    updatedAt: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(), // 2 hours ago
  },
  {
    id: "lead-4",
    email: "<EMAIL>",
    firstName: "Sarah",
    lastName: "Wilson",
    phone: "******-0126",
    company: "Creative Agency",
    jobTitle: "Creative Director",
    leadSourceId: "source-3",
    leadScore: 68,
    leadQuality: "warm",
    leadStatus: "new",
    isVerified: false,
    isDuplicate: false,
    isDeleted: false,
    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 96).toISOString(), // 4 days ago
    updatedAt: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(), // 1 day ago
  },
  {
    id: "lead-5",
    email: "<EMAIL>",
    firstName: "David",
    lastName: "Brown",
    phone: "******-0127",
    company: "Enterprise Solutions",
    jobTitle: "IT Director",
    leadSourceId: "source-4",
    leadScore: 45,
    leadQuality: "cold",
    leadStatus: "new",
    isVerified: false,
    isDuplicate: false,
    isDeleted: false,
    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 120).toISOString(), // 5 days ago
    updatedAt: new Date(Date.now() - 1000 * 60 * 60 * 48).toISOString(), // 2 days ago
  },
];

export const mockLeadSources: LeadSource[] = [
  {
    id: "source-1",
    name: "Website Contact Form",
    type: "form",
    description: "Main contact form on website",
    isActive: true,
    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 30).toISOString(), // 30 days ago
    updatedAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 7).toISOString(), // 7 days ago
  },
  {
    id: "source-2",
    name: "Social Media",
    type: "social",
    description: "Social media campaigns",
    isActive: true,
    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 25).toISOString(), // 25 days ago
    updatedAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 5).toISOString(), // 5 days ago
  },
  {
    id: "source-3",
    name: "Email Campaign",
    type: "email",
    description: "Email marketing campaigns",
    isActive: true,
    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 20).toISOString(), // 20 days ago
    updatedAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 3).toISOString(), // 3 days ago
  },
  {
    id: "source-4",
    name: "Referral",
    type: "referral",
    description: "Customer referrals",
    isActive: true,
    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 15).toISOString(), // 15 days ago
    updatedAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 1).toISOString(), // 1 day ago
  },
];

// Mock functions that return promises to simulate async behavior
export const mockLeadDatabase = {
  async getDashboardStats(): Promise<DashboardStats> {
    await new Promise(resolve => setTimeout(resolve, 100)); // Simulate network delay
    return mockDashboardStats;
  },

  async getRecentActivities(limit: number = 10): Promise<RecentActivity[]> {
    await new Promise(resolve => setTimeout(resolve, 150));
    return mockRecentActivities.slice(0, limit);
  },

  async getLeads(options: any = {}): Promise<{ leads: Lead[]; total: number }> {
    await new Promise(resolve => setTimeout(resolve, 200));
    const { page = 1, limit = 50 } = options;
    const start = (page - 1) * limit;
    const end = start + limit;
    return {
      leads: mockLeads.slice(start, end),
      total: mockLeads.length
    };
  },

  async getLeadSources(): Promise<LeadSource[]> {
    await new Promise(resolve => setTimeout(resolve, 100));
    return mockLeadSources;
  }
};

export const mockAnalyticsDatabase = {
  async getLeadTrends(days: number = 30): Promise<LeadTrendData[]> {
    await new Promise(resolve => setTimeout(resolve, 150));
    return mockLeadTrends;
  },

  async getSourceDistribution(): Promise<SourceDistribution[]> {
    await new Promise(resolve => setTimeout(resolve, 100));
    return mockSourceDistribution;
  },

  async getConversionFunnel(): Promise<ConversionFunnelData[]> {
    await new Promise(resolve => setTimeout(resolve, 120));
    return mockConversionFunnel;
  },

  async getLeadScoreDistribution(): Promise<ChartDataPoint[]> {
    await new Promise(resolve => setTimeout(resolve, 100));
    return mockLeadScoreDistribution;
  },

  async getQualityTrends(days: number = 30): Promise<ChartDataPoint[]> {
    await new Promise(resolve => setTimeout(resolve, 130));
    return mockQualityTrends;
  }
};
