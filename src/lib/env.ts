/**
 * Environment configuration and validation
 */

export const env = {
  // Required environment variables
  OPENROUTER_API_KEY: process.env.OPENROUTER_API_KEY,

  // Supabase environment variables
  NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL,
  NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,

  // Optional environment variables with defaults
  NODE_ENV: process.env.NODE_ENV || "development",
  MODEL_NAME:
    process.env.MODEL_NAME || "deepseek/deepseek-r1-0528-qwen3-8b:free",
  REDIS_URL: process.env.REDIS_URL || "redis://localhost:6379",

  // Deployment-specific
  VERCEL_URL: process.env.VERCEL_URL,
  PORT: process.env.PORT || "3000",
} as const;

/**
 * Validates required environment variables
 */
export function validateEnv() {
  const errors: string[] = [];

  if (!env.OPENROUTER_API_KEY) {
    errors.push("OPENROUTER_API_KEY is required");
  }

  if (!env.NEXT_PUBLIC_SUPABASE_URL) {
    errors.push("NEXT_PUBLIC_SUPABASE_URL is required");
  }

  if (!env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
    errors.push("NEXT_PUBLIC_SUPABASE_ANON_KEY is required");
  }

  if (env.NODE_ENV === "production" && !env.REDIS_URL?.startsWith("redis://")) {
    errors.push(
      "REDIS_URL must be a valid Redis connection string in production"
    );
  }

  if (errors.length > 0) {
    throw new Error(`Environment validation failed:\n${errors.join("\n")}`);
  }

  return true;
}

/**
 * Gets the base URL for the application
 */
export function getBaseUrl(req?: Request): string {
  // In production on Vercel
  if (env.VERCEL_URL) {
    return `https://${env.VERCEL_URL}`;
  }

  // From request URL
  if (req) {
    const url = new URL(req.url);
    return `${url.protocol}//${url.host}`;
  }

  // Fallback for development
  return `http://localhost:${env.PORT}`;
}
