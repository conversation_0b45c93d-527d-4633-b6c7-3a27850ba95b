import { createClient } from "@/utils/supabase/server";
import type {
  Lead,
  LeadSource,
  LeadActivity,
  DashboardStats,
  LeadsBySource,
  RecentActivity,
  DailyAnalytics,
  CampaignPerformance,
} from "@/lib/types";

export class LeadDatabase {
  private getSupabase() {
    return createClient();
  }

  // Dashboard Statistics
  async getDashboardStats(): Promise<DashboardStats> {
    const supabase = await this.getSupabase();

    // Get total leads count
    const { count: totalLeads } = await supabase
      .from("leads")
      .select("*", { count: "exact", head: true })
      .eq("is_deleted", false);

    // Get today's new leads
    const today = new Date().toISOString().split("T")[0];
    const { count: newLeadsToday } = await supabase
      .from("leads")
      .select("*", { count: "exact", head: true })
      .eq("is_deleted", false)
      .gte("created_at", `${today}T00:00:00.000Z`)
      .lt("created_at", `${today}T23:59:59.999Z`);

    // Get leads by quality
    const { data: qualityData } = await supabase
      .from("leads")
      .select("lead_quality")
      .eq("is_deleted", false);

    const hotLeads =
      qualityData?.filter((l) => l.lead_quality === "hot").length || 0;
    const warmLeads =
      qualityData?.filter((l) => l.lead_quality === "warm").length || 0;
    const coldLeads =
      qualityData?.filter((l) => l.lead_quality === "cold").length || 0;

    // Get conversion rate
    const { count: convertedLeads } = await supabase
      .from("leads")
      .select("*", { count: "exact", head: true })
      .eq("is_deleted", false)
      .eq("lead_status", "converted");

    const conversionRate = totalLeads
      ? ((convertedLeads || 0) / totalLeads) * 100
      : 0;

    // Get average lead score
    const { data: scoreData } = await supabase
      .from("leads")
      .select("lead_score")
      .eq("is_deleted", false);

    const avgLeadScore = scoreData?.length
      ? scoreData.reduce((sum, lead) => sum + (lead.lead_score || 0), 0) /
        scoreData.length
      : 0;

    // Get top source
    const { data: sourceData } = await supabase
      .from("source_performance_view")
      .select("source_name")
      .order("total_leads", { ascending: false })
      .limit(1)
      .single();

    return {
      totalLeads: totalLeads || 0,
      newLeadsToday: newLeadsToday || 0,
      hotLeads,
      warmLeads,
      coldLeads,
      conversionRate: Math.round(conversionRate * 100) / 100,
      avgLeadScore: Math.round(avgLeadScore * 100) / 100,
      topSource: sourceData?.source_name || "N/A",
    };
  }

  // Get leads by source performance
  async getLeadsBySource(): Promise<LeadsBySource[]> {
    const supabase = await this.getSupabase();

    const { data, error } = await supabase
      .from("source_performance_view")
      .select("*")
      .order("total_leads", { ascending: false });

    if (error) throw error;

    return (
      data?.map((item) => ({
        sourceName: item.source_name,
        sourceType: item.source_type,
        totalLeads: item.total_leads || 0,
        hotLeads: item.hot_leads || 0,
        convertedLeads: item.converted_leads || 0,
        avgLeadScore: Math.round((item.avg_lead_score || 0) * 100) / 100,
        lastLeadDate: item.last_lead_date || "",
      })) || []
    );
  }

  // Get daily analytics for charts
  async getDailyAnalytics(days: number = 30): Promise<DailyAnalytics[]> {
    const supabase = await this.getSupabase();

    const { data, error } = await supabase
      .from("lead_analytics_view")
      .select("*")
      .order("date", { ascending: false })
      .limit(days);

    if (error) throw error;

    return (
      data?.map((item) => ({
        id: `${item.date}`,
        date: item.date,
        totalLeads: item.total_leads || 0,
        newLeads: item.total_leads || 0, // Same as total for daily view
        hotLeads: item.hot_leads || 0,
        warmLeads: item.warm_leads || 0,
        coldLeads: item.cold_leads || 0,
        convertedLeads: item.converted_leads || 0,
        totalRevenue: 0, // Would need to be calculated from converted leads
        topSource: "", // Would need additional query
        createdAt: new Date().toISOString(),
      })) || []
    );
  }

  // Get recent activities
  async getRecentActivities(limit: number = 10): Promise<RecentActivity[]> {
    const supabase = await this.getSupabase();

    const { data, error } = await supabase
      .from("lead_activities")
      .select(
        `
        id,
        lead_id,
        activity_type,
        description,
        created_at,
        leads!inner (
          email,
          first_name,
          last_name,
          lead_quality
        )
      `
      )
      .order("created_at", { ascending: false })
      .limit(limit);

    if (error) throw error;

    return (
      data?.map((activity) => ({
        id: activity.id,
        leadId: activity.lead_id,
        leadEmail: activity.leads.email,
        leadName:
          activity.leads.first_name && activity.leads.last_name
            ? `${activity.leads.first_name} ${activity.leads.last_name}`
            : undefined,
        activityType: activity.activity_type,
        description: activity.description || "",
        createdAt: activity.created_at,
        leadQuality: activity.leads.lead_quality as "hot" | "warm" | "cold",
      })) || []
    );
  }

  // Get campaign performance
  async getCampaignPerformance(
    days: number = 30
  ): Promise<CampaignPerformance[]> {
    const supabase = await this.getSupabase();

    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const { data, error } = await supabase
      .from("campaign_performance")
      .select("*")
      .gte("date", startDate.toISOString().split("T")[0])
      .order("date", { ascending: false });

    if (error) throw error;

    return (
      data?.map((item) => ({
        id: item.id,
        campaignName: item.campaign_name,
        utmSource: item.utm_source || undefined,
        utmMedium: item.utm_medium || undefined,
        utmCampaign: item.utm_campaign || undefined,
        leadsCount: item.leads_count || 0,
        qualifiedLeadsCount: item.qualified_leads_count || 0,
        convertedLeadsCount: item.converted_leads_count || 0,
        totalRevenue: parseFloat(item.total_revenue || "0"),
        date: item.date,
        createdAt: item.created_at,
      })) || []
    );
  }

  // Get leads with filters and pagination
  async getLeads(
    options: {
      page?: number;
      limit?: number;
      quality?: "hot" | "warm" | "cold";
      status?: "new" | "contacted" | "qualified" | "converted" | "lost";
      sourceId?: string;
      search?: string;
    } = {}
  ): Promise<{ leads: Lead[]; total: number }> {
    const supabase = await this.getSupabase();
    const { page = 1, limit = 50, quality, status, sourceId, search } = options;

    let query = supabase
      .from("leads")
      .select("*", { count: "exact" })
      .eq("is_deleted", false);

    // Apply filters
    if (quality) query = query.eq("lead_quality", quality);
    if (status) query = query.eq("lead_status", status);
    if (sourceId) query = query.eq("lead_source_id", sourceId);
    if (search) {
      query = query.or(
        `email.ilike.%${search}%,first_name.ilike.%${search}%,last_name.ilike.%${search}%,company.ilike.%${search}%`
      );
    }

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;

    const { data, error, count } = await query
      .order("created_at", { ascending: false })
      .range(from, to);

    if (error) throw error;

    const leads =
      data?.map((item) => ({
        id: item.id,
        email: item.email,
        firstName: item.first_name || undefined,
        lastName: item.last_name || undefined,
        phone: item.phone || undefined,
        company: item.company || undefined,
        jobTitle: item.job_title || undefined,
        website: item.website || undefined,
        leadSourceId: item.lead_source_id || undefined,
        utmSource: item.utm_source || undefined,
        utmMedium: item.utm_medium || undefined,
        utmCampaign: item.utm_campaign || undefined,
        utmContent: item.utm_content || undefined,
        utmTerm: item.utm_term || undefined,
        ipAddress: item.ip_address || undefined,
        country: item.country || undefined,
        state: item.state || undefined,
        city: item.city || undefined,
        timezone: item.timezone || undefined,
        leadScore: item.lead_score || 0,
        leadQuality: item.lead_quality as "hot" | "warm" | "cold",
        leadStatus: item.lead_status as
          | "new"
          | "contacted"
          | "qualified"
          | "converted"
          | "lost",
        isVerified: item.is_verified || false,
        isDuplicate: item.is_duplicate || false,
        duplicateOf: item.duplicate_of || undefined,
        isDeleted: item.is_deleted || false,
        createdAt: item.created_at,
        updatedAt: item.updated_at,
        lastContactedAt: item.last_contacted_at || undefined,
        convertedAt: item.converted_at || undefined,
      })) || [];

    return { leads, total: count || 0 };
  }

  // Get lead sources
  async getLeadSources(): Promise<LeadSource[]> {
    const supabase = await this.getSupabase();

    const { data, error } = await supabase
      .from("lead_sources")
      .select("*")
      .eq("is_active", true)
      .order("name");

    if (error) throw error;

    return (
      data?.map((item) => ({
        id: item.id,
        name: item.name,
        type: item.type,
        url: item.url || undefined,
        description: item.description || undefined,
        isActive: item.is_active || false,
        createdAt: item.created_at,
        updatedAt: item.updated_at,
      })) || []
    );
  }
}

// Class is already exported above
