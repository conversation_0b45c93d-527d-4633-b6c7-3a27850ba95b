import { createClient } from "@/utils/supabase/server";

// Sample data for testing the dashboard
const sampleLeads = [
  {
    email: "<EMAIL>",
    first_name: "<PERSON>",
    last_name: "<PERSON><PERSON>",
    phone: "******-0123",
    company: "Tech Corp",
    job_title: "Software Engineer",
    lead_quality: "hot",
    lead_status: "new",
    lead_score: 85,
    country: "United States",
    state: "California",
    city: "San Francisco"
  },
  {
    email: "<EMAIL>",
    first_name: "<PERSON>",
    last_name: "<PERSON>",
    phone: "******-0124",
    company: "Startup Inc",
    job_title: "CTO",
    lead_quality: "warm",
    lead_status: "contacted",
    lead_score: 72,
    country: "United States",
    state: "New York",
    city: "New York"
  },
  {
    email: "<EMAIL>",
    first_name: "<PERSON>",
    last_name: "<PERSON>",
    phone: "******-0125",
    company: "Big Corp",
    job_title: "VP Engineering",
    lead_quality: "hot",
    lead_status: "qualified",
    lead_score: 91,
    country: "United States",
    state: "Texas",
    city: "Austin"
  },
  {
    email: "<EMAIL>",
    first_name: "<PERSON>",
    last_name: "<PERSON>",
    phone: "******-0126",
    company: "Creative Agency",
    job_title: "Creative Director",
    lead_quality: "warm",
    lead_status: "new",
    lead_score: 68,
    country: "Canada",
    state: "Ontario",
    city: "Toronto"
  },
  {
    email: "<EMAIL>",
    first_name: "David",
    last_name: "Brown",
    phone: "******-0127",
    company: "Enterprise Solutions",
    job_title: "IT Director",
    lead_quality: "cold",
    lead_status: "new",
    lead_score: 45,
    country: "United Kingdom",
    state: "England",
    city: "London"
  }
];

const sampleActivities = [
  {
    activity_type: "form_submit",
    description: "Submitted contact form on homepage",
    performed_by: "system"
  },
  {
    activity_type: "email_open",
    description: "Opened welcome email",
    performed_by: "system"
  },
  {
    activity_type: "link_click",
    description: "Clicked pricing page link",
    performed_by: "system"
  },
  {
    activity_type: "call",
    description: "Initial discovery call completed",
    performed_by: "<EMAIL>"
  },
  {
    activity_type: "meeting",
    description: "Demo meeting scheduled",
    performed_by: "<EMAIL>"
  }
];

export class DatabaseSeeder {
  private supabase;

  constructor() {
    this.supabase = createClient();
  }

  async seedLeadSources() {
    const supabase = await this.supabase;
    
    // Check if sources already exist
    const { data: existingSources } = await supabase
      .from('lead_sources')
      .select('id')
      .limit(1);

    if (existingSources && existingSources.length > 0) {
      console.log('Lead sources already exist, skipping...');
      return;
    }

    const sources = [
      {
        name: "Website Contact Form",
        type: "form",
        description: "Main contact form on website"
      },
      {
        name: "Newsletter Signup",
        type: "form", 
        description: "Email newsletter subscription"
      },
      {
        name: "Landing Page Form",
        type: "form",
        description: "Lead generation landing page"
      },
      {
        name: "Chat Widget",
        type: "chat",
        description: "Website chat interactions"
      },
      {
        name: "Social Media",
        type: "social",
        description: "Social media campaigns"
      }
    ];

    const { error } = await supabase
      .from('lead_sources')
      .insert(sources);

    if (error) {
      console.error('Error seeding lead sources:', error);
      throw error;
    }

    console.log('Lead sources seeded successfully');
  }

  async seedSampleLeads() {
    const supabase = await this.supabase;
    
    // Check if leads already exist
    const { data: existingLeads } = await supabase
      .from('leads')
      .select('id')
      .limit(1);

    if (existingLeads && existingLeads.length > 0) {
      console.log('Leads already exist, skipping...');
      return;
    }

    // Get lead sources
    const { data: sources } = await supabase
      .from('lead_sources')
      .select('id, name');

    if (!sources || sources.length === 0) {
      throw new Error('No lead sources found. Please seed lead sources first.');
    }

    // Add lead_source_id to sample leads
    const leadsWithSources = sampleLeads.map((lead, index) => ({
      ...lead,
      lead_source_id: sources[index % sources.length].id
    }));

    const { data: insertedLeads, error } = await supabase
      .from('leads')
      .insert(leadsWithSources)
      .select('id');

    if (error) {
      console.error('Error seeding leads:', error);
      throw error;
    }

    console.log('Sample leads seeded successfully');
    return insertedLeads;
  }

  async seedSampleActivities() {
    const supabase = await this.supabase;
    
    // Get existing leads
    const { data: leads } = await supabase
      .from('leads')
      .select('id')
      .limit(10);

    if (!leads || leads.length === 0) {
      throw new Error('No leads found. Please seed leads first.');
    }

    // Create activities for each lead
    const activities = [];
    for (const lead of leads) {
      // Add 1-3 random activities per lead
      const numActivities = Math.floor(Math.random() * 3) + 1;
      for (let i = 0; i < numActivities; i++) {
        const activity = sampleActivities[Math.floor(Math.random() * sampleActivities.length)];
        activities.push({
          lead_id: lead.id,
          ...activity,
          created_at: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString() // Random time in last 7 days
        });
      }
    }

    const { error } = await supabase
      .from('lead_activities')
      .insert(activities);

    if (error) {
      console.error('Error seeding activities:', error);
      throw error;
    }

    console.log('Sample activities seeded successfully');
  }

  async seedDailyAnalytics() {
    const supabase = await this.supabase;
    
    // Check if analytics already exist
    const { data: existingAnalytics } = await supabase
      .from('daily_analytics')
      .select('id')
      .limit(1);

    if (existingAnalytics && existingAnalytics.length > 0) {
      console.log('Daily analytics already exist, skipping...');
      return;
    }

    // Generate analytics for the last 30 days
    const analytics = [];
    for (let i = 29; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      
      const totalLeads = Math.floor(Math.random() * 20) + 5; // 5-25 leads per day
      const hotLeads = Math.floor(totalLeads * 0.2); // 20% hot
      const warmLeads = Math.floor(totalLeads * 0.3); // 30% warm
      const coldLeads = totalLeads - hotLeads - warmLeads; // Rest cold
      const convertedLeads = Math.floor(hotLeads * 0.3); // 30% of hot leads convert
      
      analytics.push({
        date: date.toISOString().split('T')[0],
        total_leads: totalLeads,
        new_leads: totalLeads,
        hot_leads: hotLeads,
        warm_leads: warmLeads,
        cold_leads: coldLeads,
        converted_leads: convertedLeads,
        total_revenue: convertedLeads * 1000, // $1000 per conversion
        top_source: 'Website Contact Form'
      });
    }

    const { error } = await supabase
      .from('daily_analytics')
      .insert(analytics);

    if (error) {
      console.error('Error seeding daily analytics:', error);
      throw error;
    }

    console.log('Daily analytics seeded successfully');
  }

  async seedAll() {
    try {
      console.log('Starting database seeding...');
      
      await this.seedLeadSources();
      await this.seedSampleLeads();
      await this.seedSampleActivities();
      await this.seedDailyAnalytics();
      
      console.log('Database seeding completed successfully!');
    } catch (error) {
      console.error('Error during database seeding:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const dbSeeder = new DatabaseSeeder();
