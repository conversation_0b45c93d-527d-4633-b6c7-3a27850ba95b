import "server-only";

interface N8nConfig {
  baseUrl: string;
  apiKey: string;
}

function getN8nConfig(): N8nConfig {
  const baseUrl =
    process.env.N8N_BASE_URL ?? "http://localhost:5678/webhook-test";
  const apiKey = process.env.N8N_WEBHOOK_API_KEY;

  if (!baseUrl || !apiKey) {
    throw new Error("N8N configuration missing");
  }

  return { baseUrl, apiKey };
}

export async function sendToN8nWebhook(data: unknown, path: string) {
  // ❌ This function CANNOT be imported by client components
  // ✅ Secrets are completely isolated

  const config = getN8nConfig();

  const response = await fetch(`${config.baseUrl}${path}`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "X-API-Key": config.apiKey,
    },
    body: JSON.stringify({
      data,
    }),
  });
  if (!response.ok) {
    const errorText = await response.text();
    console.log("Error response:", errorText);
    throw new Error(`N8N webhook failed: ${response.status} - ${errorText}`);
  }

  return response.json();
}
