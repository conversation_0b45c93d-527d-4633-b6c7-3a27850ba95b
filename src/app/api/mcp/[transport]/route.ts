import { createMcpHand<PERSON> } from "@vercel/mcp-adapter";
import { z } from "zod";
import { env } from "@/lib/env";

const handler = createMcpHandler(
  (server) => {
    server.tool(
      "roll_dice",
      "Rolls an N-sided die",
      { sides: z.number().int().min(2) },
      async ({ sides }) => {
        const value = 1 + Math.floor(Math.random() * sides);
        return {
          content: [{ type: "text", text: `🎲 You rolled a ${value}!` }],
        };
      }
    );
  },
  {
    capabilities: {
      tools: {
        echo: {
          description: "Roll the dice",
        },
      },
    },
  },
  {
    basePath: "/api/mcp",
    verboseLogs: env.NODE_ENV === "development",
    maxDuration: 60,
    redisUrl: env.REDIS_URL,
  }
);

export { handler as GET, handler as POST };
