import { experimental_createMCPClient, streamText, type CoreMessage } from "ai";
import { createOpenRouter } from "@openrouter/ai-sdk-provider";
import { env, validateEnv, getBaseUrl } from "@/lib/env";

// Validate environment on module load
try {
  validateEnv();
} catch (error) {
  console.error("Environment validation failed:", error);
}

const openrouter = createOpenRouter({
  apiKey: env.OPENROUTER_API_KEY || "",
});

export async function POST(req: Request) {
  // Input validation
  if (!req.body) {
    return new Response(JSON.stringify({ error: "Request body is required" }), {
      status: 400,
      headers: { "Content-Type": "application/json" },
    });
  }

  let body: { messages: CoreMessage[] };
  try {
    body = await req.json();
  } catch {
    return new Response(
      JSON.stringify({ error: "Invalid JSON in request body" }),
      { status: 400, headers: { "Content-Type": "application/json" } }
    );
  }

  const { messages } = body;

  // Validate messages
  if (!messages || !Array.isArray(messages) || messages.length === 0) {
    return new Response(
      JSON.stringify({
        error: "Messages array is required and cannot be empty",
      }),
      { status: 400, headers: { "Content-Type": "application/json" } }
    );
  }

  let tools = {};
  try {
    // Get the base URL dynamically for deployment
    const baseUrl = getBaseUrl(req);

    // Connect to your MCP server in the same app
    const mcpClient = await experimental_createMCPClient({
      transport: {
        type: "sse",
        url: `${baseUrl}/api/mcp/sse`,
      },
    });

    console.log("Connected to MCP server:", mcpClient);

    // Get tools from MCP server
    tools = await mcpClient.tools();
    console.log("Available tools:", Object.keys(tools));
  } catch (error) {
    console.error("Error connecting to MCP server:", error);
    // Continue without MCP tools instead of returning error
    console.log("Continuing without MCP tools");
  }

  try {
    const chatModel = openrouter.chat(env.MODEL_NAME);

    const response = await streamText({
      model: chatModel,
      messages,
      tools, // Include MCP tools
    });

    return response.toDataStreamResponse();
  } catch (error) {
    console.error("Error in streamText:", error);
    console.error("Error details:", JSON.stringify(error, null, 2));
    return new Response(
      JSON.stringify({
        error: "Internal Server Error",
        details: error instanceof Error ? error.message : String(error),
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" },
      }
    );
  }
}
