import { NextResponse } from "next/server";
import { LeadDatabase } from "@/lib/database/leads";

export async function GET() {
  try {
    const leadDb = new LeadDatabase();
    const stats = await leadDb.getDashboardStats();
    return NextResponse.json({ success: true, data: stats });
  } catch (error) {
    console.error("Error fetching dashboard stats:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch dashboard stats" },
      { status: 500 }
    );
  }
}
