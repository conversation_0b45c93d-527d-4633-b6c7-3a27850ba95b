import { LeadForm, NewsletterForm } from "@/components/forms";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON>bsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Mail, Download, MessageSquare, Users } from "lucide-react";
import Link from "next/link";

export default function FormsPage() {
  return (
    <div className="container mx-auto py-8 px-4">
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold mb-4">Form Components</h1>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Explore our comprehensive form components built with TypeScript, Zod validation, 
          and server actions for robust lead generation and user engagement.
        </p>
      </div>

      <Tabs defaultValue="contact" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="contact" className="flex items-center gap-2">
            <MessageSquare className="h-4 w-4" />
            Contact
          </TabsTrigger>
          <TabsTrigger value="lead" className="flex items-center gap-2">
            <Download className="h-4 w-4" />
            Lead Gen
          </TabsTrigger>
          <TabsTrigger value="newsletter" className="flex items-center gap-2">
            <Mail className="h-4 w-4" />
            Newsletter
          </TabsTrigger>
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Overview
          </TabsTrigger>
        </TabsList>

        <TabsContent value="contact" className="mt-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MessageSquare className="h-5 w-5" />
                  Contact Form
                </CardTitle>
                <CardDescription>
                  Full-featured contact form with optional fields for better lead qualification.
                  Includes validation, error handling, and success states.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="bg-muted p-4 rounded-lg">
                    <h4 className="font-semibold mb-2">Features:</h4>
                    <ul className="text-sm space-y-1 text-muted-foreground">
                      <li>• Required fields: Name, Email, Message</li>
                      <li>• Optional fields: Company, Job Title, Phone, Website</li>
                      <li>• Real-time validation with Zod</li>
                      <li>• UTM parameter tracking</li>
                      <li>• N8N webhook integration</li>
                    </ul>
                  </div>
                  <Link 
                    href="/form" 
                    className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 w-full"
                  >
                    View Contact Form
                  </Link>
                </div>
              </CardContent>
            </Card>

            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Schema Validation</CardTitle>
                </CardHeader>
                <CardContent>
                  <pre className="text-xs bg-muted p-3 rounded overflow-x-auto">
{`contactFormSchema = z.object({
  name: z.string().min(2).max(100),
  email: z.string().email(),
  message: z.string().min(10).max(1000),
  company: z.string().max(200).optional(),
  jobTitle: z.string().max(150).optional(),
  phone: z.string().max(20).optional(),
  website: z.string().url().optional()
})`}
                  </pre>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Server Action</CardTitle>
                </CardHeader>
                <CardContent>
                  <pre className="text-xs bg-muted p-3 rounded overflow-x-auto">
{`export async function submitContactForm(
  prevState: FormSubmissionResult | null,
  formData: FormData
): Promise<FormSubmissionResult>`}
                  </pre>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="lead" className="mt-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <LeadForm 
              title="Free Marketing Guide"
              description="Download our comprehensive marketing guide and learn proven strategies to grow your business."
              leadMagnet="marketing-guide-2024"
              interests={["Digital Marketing", "Content Strategy", "SEO", "Social Media"]}
            />

            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Download className="h-5 w-5" />
                    Lead Generation Form
                  </CardTitle>
                  <CardDescription>
                    Optimized for lead capture with customizable lead magnets and interest tracking.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="bg-muted p-4 rounded-lg">
                      <h4 className="font-semibold mb-2">Features:</h4>
                      <ul className="text-sm space-y-1 text-muted-foreground">
                        <li>• Required: First Name, Last Name, Email, Company</li>
                        <li>• Optional: Job Title, Phone, Website</li>
                        <li>• Customizable interest checkboxes</li>
                        <li>• Lead magnet tracking</li>
                        <li>• Lead scoring integration</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Usage Example</CardTitle>
                </CardHeader>
                <CardContent>
                  <pre className="text-xs bg-muted p-3 rounded overflow-x-auto">
{`<LeadForm 
  title="Free Guide"
  leadMagnet="guide-2024"
  interests={["Marketing", "Sales"]}
/>`}
                  </pre>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="newsletter" className="mt-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <NewsletterForm 
              title="Join Our Community"
              description="Get weekly insights, tips, and exclusive content delivered to your inbox."
              interests={["Weekly Newsletter", "Product Updates", "Industry Insights", "Expert Tips"]}
            />

            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Mail className="h-5 w-5" />
                    Newsletter Signup
                  </CardTitle>
                  <CardDescription>
                    Simple newsletter subscription with interest preferences and GDPR compliance.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="bg-muted p-4 rounded-lg">
                      <h4 className="font-semibold mb-2">Features:</h4>
                      <ul className="text-sm space-y-1 text-muted-foreground">
                        <li>• Required: Email address only</li>
                        <li>• Optional: First name, interests</li>
                        <li>• Card or inline variants</li>
                        <li>• Privacy-focused messaging</li>
                        <li>• Easy unsubscribe</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Inline Variant</CardTitle>
                </CardHeader>
                <CardContent>
                  <NewsletterForm 
                    variant="inline"
                    title="Quick Subscribe"
                    description="Stay in the loop with our latest updates."
                    interests={[]}
                  />
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="overview" className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Type Safety
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  All forms use TypeScript interfaces and Zod schemas for complete type safety 
                  from client to server.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MessageSquare className="h-5 w-5" />
                  Server Actions
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Built with Next.js server actions for seamless form submission without 
                  API routes.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Download className="h-5 w-5" />
                  N8N Integration
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Automatic webhook integration with N8N for lead processing, scoring, 
                  and CRM synchronization.
                </p>
              </CardContent>
            </Card>
          </div>

          <Card className="mt-6">
            <CardHeader>
              <CardTitle>Available Form Actions</CardTitle>
              <CardDescription>
                All form actions are defined in <code>src/actions/form.ts</code>
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold mb-2">Form Submissions</h4>
                  <ul className="text-sm space-y-1 text-muted-foreground">
                    <li>• <code>submitContactForm</code></li>
                    <li>• <code>submitLeadForm</code></li>
                    <li>• <code>submitNewsletterForm</code></li>
                    <li>• <code>submitChatLead</code></li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Utility Actions</h4>
                  <ul className="text-sm space-y-1 text-muted-foreground">
                    <li>• <code>crawlWebsite</code></li>
                    <li>• Client metadata extraction</li>
                    <li>• UTM parameter handling</li>
                    <li>• Error handling & validation</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
