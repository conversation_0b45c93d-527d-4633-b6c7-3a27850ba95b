@import "tailwindcss";
@import "tw-animate-css";
@plugin "@tailwindcss/typography";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --background: oklch(0.98 0.01 240);
  --foreground: oklch(0.25 0.01 240);
  --card: oklch(0.99 0.005 240);
  --card-foreground: oklch(0.25 0.01 240);
  --popover: oklch(0.99 0.005 240);
  --popover-foreground: oklch(0.25 0.01 240);
  --primary: oklch(0.55 0.18 230);
  --primary-foreground: oklch(0.98 0.005 240);
  --secondary: oklch(0.96 0.03 240);
  --secondary-foreground: oklch(0.3 0.05 230);
  --muted: oklch(0.95 0.02 240);
  --muted-foreground: oklch(0.5 0.03 240);
  --accent: oklch(0.96 0.03 240);
  --accent-foreground: oklch(0.3 0.05 230);
  --destructive: oklch(0.55 0.18 30);
  --border: oklch(0.9 0.02 240);
  --input: oklch(0.85 0.02 240);
  --ring: oklch(0.55 0.15 230);
  --radius: 0.5rem;
  --sidebar: oklch(0.97 0.015 240);
  --sidebar-foreground: oklch(0.25 0.01 240);
  --sidebar-primary: oklch(0.55 0.18 230);
  --sidebar-primary-foreground: oklch(0.98 0.005 240);
  --sidebar-accent: oklch(0.96 0.03 240);
  --sidebar-accent-foreground: oklch(0.3 0.05 230);
  --sidebar-border: oklch(0.88 0.02 240);
  --sidebar-ring: oklch(0.55 0.15 230);
  --chart-1: oklch(0.55 0.18 230);
  --chart-2: oklch(0.65 0.15 180);
  --chart-3: oklch(0.6 0.16 270);
  --chart-4: oklch(0.7 0.14 130);
  --chart-5: oklch(0.5 0.17 30);
}

.dark {
  --background: oklch(0.2 0.01 240);
  --foreground: oklch(0.9 0.01 240);
  --card: oklch(0.22 0.015 240);
  --card-foreground: oklch(0.9 0.01 240);
  --popover: oklch(0.22 0.015 240);
  --popover-foreground: oklch(0.9 0.01 240);
  --primary: oklch(0.65 0.15 230);
  --primary-foreground: oklch(0.15 0.01 240);
  --secondary: oklch(0.27 0.03 240);
  --secondary-foreground: oklch(0.85 0.03 230);
  --muted: oklch(0.25 0.02 240);
  --muted-foreground: oklch(0.7 0.03 240);
  --accent: oklch(0.27 0.03 240);
  --accent-foreground: oklch(0.85 0.03 230);
  --destructive: oklch(0.65 0.15 30);
  --border: oklch(0.3 0.02 240);
  --input: oklch(0.35 0.02 240);
  --ring: oklch(0.65 0.12 230);
  --sidebar: oklch(0.18 0.015 240);
  --sidebar-foreground: oklch(0.9 0.01 240);
  --sidebar-primary: oklch(0.65 0.15 230);
  --sidebar-primary-foreground: oklch(0.15 0.01 240);
  --sidebar-accent: oklch(0.27 0.03 240);
  --sidebar-accent-foreground: oklch(0.85 0.03 230);
  --sidebar-border: oklch(0.28 0.02 240);
  --sidebar-ring: oklch(0.65 0.12 230);
  --chart-1: oklch(0.65 0.15 230);
  --chart-2: oklch(0.7 0.12 180);
  --chart-3: oklch(0.68 0.13 270);
  --chart-4: oklch(0.75 0.11 130);
  --chart-5: oklch(0.6 0.14 30);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
