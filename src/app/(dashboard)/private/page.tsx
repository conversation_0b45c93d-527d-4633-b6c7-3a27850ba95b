import { redirect } from "next/navigation";
import { createClient } from "@/utils/supabase/server";
import { Suspense } from "react";
import { LeadDatabase } from "@/lib/database/leads";
import { AnalyticsDatabase } from "@/lib/database/analytics";
import {
  DashboardStatsCards,
  LeadQualityBreakdown,
} from "@/components/dashboard/dashboard-stats";
import {
  LeadTrendsChart,
  SourceDistributionChart,
  ConversionFunnelChart,
  LeadScoreChart,
  QualityTrendsChart,
} from "@/components/dashboard/charts";
import {
  RecentActivities,
  ActivitySummary,
} from "@/components/dashboard/recent-activities";
import { LeadsTable } from "@/components/dashboard/leads-table";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { logout } from "@/app/(auth)/login/actions";
import { BarChart3, TrendingUp, Users, Activity } from "lucide-react";

// Loading components
function StatsLoading() {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6">
      {Array.from({ length: 6 }).map((_, i) => (
        <Card key={i}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-8 w-8 rounded-full" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-8 w-16 mb-2" />
            <Skeleton className="h-3 w-24" />
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

function ChartLoading() {
  return (
    <Card>
      <CardHeader>
        <Skeleton className="h-6 w-32" />
        <Skeleton className="h-4 w-48" />
      </CardHeader>
      <CardContent>
        <Skeleton className="h-64 w-full" />
      </CardContent>
    </Card>
  );
}

// Data fetching components
async function DashboardStats() {
  try {
    const leadDb = new LeadDatabase();
    const stats = await leadDb.getDashboardStats();
    return (
      <>
        <DashboardStatsCards stats={stats} />
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          <LeadQualityBreakdown
            hotLeads={stats.hotLeads}
            warmLeads={stats.warmLeads}
            coldLeads={stats.coldLeads}
          />
        </div>
      </>
    );
  } catch (error) {
    console.error("Error fetching dashboard stats:", error);
    return (
      <Card>
        <CardContent className="p-6">
          <p className="text-muted-foreground">
            Unable to load dashboard statistics
          </p>
        </CardContent>
      </Card>
    );
  }
}

async function DashboardCharts() {
  try {
    const analyticsDb = new AnalyticsDatabase();
    const [
      leadTrends,
      sourceDistribution,
      conversionFunnel,
      leadScoreDistribution,
      qualityTrends,
    ] = await Promise.all([
      analyticsDb.getLeadTrends(30),
      analyticsDb.getSourceDistribution(),
      analyticsDb.getConversionFunnel(),
      analyticsDb.getLeadScoreDistribution(),
      analyticsDb.getQualityTrends(30),
    ]);

    return (
      <div className="grid gap-6">
        <div className="grid gap-6 lg:grid-cols-2">
          <LeadTrendsChart data={leadTrends} />
          <SourceDistributionChart data={sourceDistribution} />
        </div>
        <div className="grid gap-6 lg:grid-cols-2">
          <ConversionFunnelChart data={conversionFunnel} />
          <LeadScoreChart data={leadScoreDistribution} />
        </div>
        <QualityTrendsChart data={qualityTrends} />
      </div>
    );
  } catch (error) {
    console.error("Error fetching chart data:", error);
    return (
      <Card>
        <CardContent className="p-6">
          <p className="text-muted-foreground">Unable to load charts</p>
        </CardContent>
      </Card>
    );
  }
}

async function DashboardActivities() {
  try {
    const leadDb = new LeadDatabase();
    const activities = await leadDb.getRecentActivities(10);
    return (
      <div className="grid gap-6 lg:grid-cols-3">
        <div className="lg:col-span-2">
          <RecentActivities activities={activities} />
        </div>
        <ActivitySummary activities={activities} />
      </div>
    );
  } catch (error) {
    console.error("Error fetching activities:", error);
    return (
      <Card>
        <CardContent className="p-6">
          <p className="text-muted-foreground">Unable to load activities</p>
        </CardContent>
      </Card>
    );
  }
}

async function DashboardLeadsTable() {
  try {
    const leadDb = new LeadDatabase();
    const [{ leads, total }, sources] = await Promise.all([
      leadDb.getLeads({ page: 1, limit: 20 }),
      leadDb.getLeadSources(),
    ]);

    return (
      <LeadsTable
        leads={leads}
        sources={sources}
        total={total}
        currentPage={1}
        pageSize={20}
      />
    );
  } catch (error) {
    console.error("Error fetching leads:", error);
    return (
      <Card>
        <CardContent className="p-6">
          <p className="text-muted-foreground">Unable to load leads table</p>
        </CardContent>
      </Card>
    );
  }
}

export default async function PrivatePage() {
  const supabase = await createClient();
  const { data, error } = await supabase.auth.getUser();

  if (error || !data?.user) {
    redirect("/login");
  }

  return (
    <div className="container mx-auto py-8 px-4 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Lead Analytics Dashboard</h1>
          <p className="text-muted-foreground">
            Track and analyze your lead generation performance
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <BarChart3 className="h-4 w-4 mr-2" />
            Export Report
          </Button>
          <form action={logout} className="inline">
            <Button type="submit" variant="outline" size="sm">
              Sign Out
            </Button>
          </form>
        </div>
      </div>

      {/* Dashboard Stats */}
      <Suspense fallback={<StatsLoading />}>
        <DashboardStats />
      </Suspense>

      {/* Charts Section */}
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <TrendingUp className="h-5 w-5" />
          <h2 className="text-2xl font-semibold">Analytics & Trends</h2>
        </div>
        <Suspense
          fallback={
            <div className="grid gap-6">
              <ChartLoading />
              <ChartLoading />
            </div>
          }
        >
          <DashboardCharts />
        </Suspense>
      </div>

      {/* Activities Section */}
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <Activity className="h-5 w-5" />
          <h2 className="text-2xl font-semibold">Recent Activities</h2>
        </div>
        <Suspense fallback={<ChartLoading />}>
          <DashboardActivities />
        </Suspense>
      </div>

      {/* Leads Table Section */}
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <Users className="h-5 w-5" />
          <h2 className="text-2xl font-semibold">Recent Leads</h2>
        </div>
        <Suspense fallback={<ChartLoading />}>
          <DashboardLeadsTable />
        </Suspense>
      </div>
    </div>
  );
}
