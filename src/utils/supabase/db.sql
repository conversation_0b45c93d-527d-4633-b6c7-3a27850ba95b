-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Lead Sources Table
CREATE TABLE lead_sources (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL UNIQUE,
    type VARCHAR(50) NOT NULL, -- 'form', 'chat', 'api', 'import', etc.
    url TEXT,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Leads Table (Main table)
CREATE TABLE leads (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) NOT NULL,
    first_name VA<PERSON><PERSON><PERSON>(100),
    last_name VARCHAR(100),
    phone VARCHAR(20),
    company VARCHAR(200),
    job_title VARCHAR(150),
    website VARCHAR(255),
    
    -- Lead Details
    lead_source_id UUID REFERENCES lead_sources(id),
    utm_source VARCHAR(100),
    utm_medium VARCHAR(100),
    utm_campaign VARCHAR(100),
    utm_content VARCHAR(100),
    utm_term VARCHAR(100),
    
    -- Enriched Data
    ip_address INET,
    country VARCHAR(100),
    state VARCHAR(100),
    city VARCHAR(100),
    timezone VARCHAR(50),
    
    -- Lead Scoring & Classification
    lead_score INTEGER DEFAULT 0,
    lead_quality VARCHAR(20) DEFAULT 'cold', -- 'hot', 'warm', 'cold'
    lead_status VARCHAR(30) DEFAULT 'new', -- 'new', 'contacted', 'qualified', 'converted', 'lost'
    
    -- Flags
    is_verified BOOLEAN DEFAULT false,
    is_duplicate BOOLEAN DEFAULT false,
    duplicate_of UUID REFERENCES leads(id),
    is_deleted BOOLEAN DEFAULT false,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_contacted_at TIMESTAMP WITH TIME ZONE,
    converted_at TIMESTAMP WITH TIME ZONE,
    
    -- Constraints
    UNIQUE(email),
    CHECK (lead_quality IN ('hot', 'warm', 'cold')),
    CHECK (lead_status IN ('new', 'contacted', 'qualified', 'converted', 'lost'))
);

-- Lead Activities Table (for tracking interactions)
CREATE TABLE lead_activities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    lead_id UUID NOT NULL REFERENCES leads(id) ON DELETE CASCADE,
    activity_type VARCHAR(50) NOT NULL, -- 'form_submit', 'email_open', 'link_click', 'call', 'meeting', etc.
    description TEXT,
    metadata JSONB, -- Store additional data like form fields, email subject, etc.
    performed_by VARCHAR(100), -- user email or 'system'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Lead Notes Table
CREATE TABLE lead_notes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    lead_id UUID NOT NULL REFERENCES leads(id) ON DELETE CASCADE,
    note TEXT NOT NULL,
    created_by VARCHAR(100) NOT NULL, -- user email
    is_private BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Lead Tags Table (for flexible categorization)
CREATE TABLE lead_tags (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(50) NOT NULL UNIQUE,
    color VARCHAR(7) DEFAULT '#6366f1', -- hex color
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Lead Tag Assignments (Many-to-Many)
CREATE TABLE lead_tag_assignments (
    lead_id UUID REFERENCES leads(id) ON DELETE CASCADE,
    tag_id UUID REFERENCES lead_tags(id) ON DELETE CASCADE,
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    PRIMARY KEY (lead_id, tag_id)
);

-- Campaign Performance Table (for analytics)
CREATE TABLE campaign_performance (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    campaign_name VARCHAR(100) NOT NULL,
    utm_source VARCHAR(100),
    utm_medium VARCHAR(100),
    utm_campaign VARCHAR(100),
    leads_count INTEGER DEFAULT 0,
    qualified_leads_count INTEGER DEFAULT 0,
    converted_leads_count INTEGER DEFAULT 0,
    total_revenue DECIMAL(12,2) DEFAULT 0,
    date DATE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(campaign_name, utm_source, utm_medium, utm_campaign, date)
);

-- Daily Analytics Summary (for dashboard performance)
CREATE TABLE daily_analytics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    date DATE NOT NULL UNIQUE,
    total_leads INTEGER DEFAULT 0,
    new_leads INTEGER DEFAULT 0,
    hot_leads INTEGER DEFAULT 0,
    warm_leads INTEGER DEFAULT 0,
    cold_leads INTEGER DEFAULT 0,
    converted_leads INTEGER DEFAULT 0,
    total_revenue DECIMAL(12,2) DEFAULT 0,
    top_source VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create Indexes for Performance
CREATE INDEX idx_leads_email ON leads(email);
CREATE INDEX idx_leads_created_at ON leads(created_at);
CREATE INDEX idx_leads_lead_score ON leads(lead_score DESC);
CREATE INDEX idx_leads_lead_quality ON leads(lead_quality);
CREATE INDEX idx_leads_lead_status ON leads(lead_status);
CREATE INDEX idx_leads_source_id ON leads(lead_source_id);
CREATE INDEX idx_leads_is_deleted ON leads(is_deleted) WHERE is_deleted = false;

CREATE INDEX idx_lead_activities_lead_id ON lead_activities(lead_id);
CREATE INDEX idx_lead_activities_created_at ON lead_activities(created_at);
CREATE INDEX idx_lead_activities_type ON lead_activities(activity_type);

-- Create Updated At Trigger Function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add Updated At Triggers
CREATE TRIGGER update_leads_updated_at 
    BEFORE UPDATE ON leads 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_lead_sources_updated_at 
    BEFORE UPDATE ON lead_sources 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_lead_notes_updated_at 
    BEFORE UPDATE ON lead_notes 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert Default Lead Sources
INSERT INTO lead_sources (name, type, description) VALUES
    ('Website Contact Form', 'form', 'Main contact form on website'),
    ('Newsletter Signup', 'form', 'Email newsletter subscription'),
    ('Landing Page Form', 'form', 'Lead generation landing page'),
    ('Chat Widget', 'chat', 'Website chat interactions'),
    ('Lead Magnet Download', 'form', 'eBook/Resource downloads'),
    ('API Integration', 'api', 'Third-party integrations'),
    ('Manual Import', 'import', 'Manually imported leads'),
    ('Social Media', 'social', 'Social media campaigns');

-- Insert Default Tags
INSERT INTO lead_tags (name, color) VALUES
    ('VIP', '#ef4444'),
    ('Demo Request', '#f97316'),
    ('High Intent', '#eab308'),
    ('Newsletter', '#22c55e'),
    ('Webinar', '#3b82f6'),
    ('Free Trial', '#8b5cf6'),
    ('Enterprise', '#ec4899'),
    ('SMB', '#06b6d4');

-- Create View for Lead Analytics
CREATE VIEW lead_analytics_view AS
SELECT 
    DATE(created_at) as date,
    COUNT(*) as total_leads,
    COUNT(*) FILTER (WHERE lead_quality = 'hot') as hot_leads,
    COUNT(*) FILTER (WHERE lead_quality = 'warm') as warm_leads,
    COUNT(*) FILTER (WHERE lead_quality = 'cold') as cold_leads,
    COUNT(*) FILTER (WHERE lead_status = 'converted') as converted_leads,
    AVG(lead_score) as avg_lead_score
FROM leads 
WHERE is_deleted = false
GROUP BY DATE(created_at)
ORDER BY date DESC;

-- Create View for Source Performance
CREATE VIEW source_performance_view AS
SELECT 
    ls.name as source_name,
    ls.type as source_type,
    COUNT(l.id) as total_leads,
    COUNT(l.id) FILTER (WHERE l.lead_quality = 'hot') as hot_leads,
    COUNT(l.id) FILTER (WHERE l.lead_status = 'converted') as converted_leads,
    AVG(l.lead_score) as avg_lead_score,
    MAX(l.created_at) as last_lead_date
FROM lead_sources ls
LEFT JOIN leads l ON ls.id = l.lead_source_id AND l.is_deleted = false
WHERE ls.is_active = true
GROUP BY ls.id, ls.name, ls.type
ORDER BY total_leads DESC;