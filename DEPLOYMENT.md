# Deployment Checklist

## Pre-Deployment Checklist

### 1. Environment Setup
- [ ] OpenRouter API key obtained and tested
- [ ] Redis instance configured (local for dev, cloud for production)
- [ ] Environment variables configured in `.env.local`
- [ ] All required environment variables validated

### 2. Code Quality
- [ ] TypeScript compilation passes: `npm run type-check`
- [ ] Linting passes: `npm run lint`
- [ ] Build succeeds: `npm run build`
- [ ] All tests pass (if any)

### 3. Functionality Testing
- [ ] Health check endpoint works: `GET /api/health`
- [ ] Chat API responds: `POST /api/chat`
- [ ] MCP tools are discoverable
- [ ] Dice rolling tool works
- [ ] Error handling works properly
- [ ] Frontend displays correctly

### 4. Production Configuration
- [ ] `vercel.json` configured
- [ ] Environment variables set in deployment platform
- [ ] Redis URL configured for production
- [ ] Verbose logging disabled in production
- [ ] CORS headers configured if needed

## Deployment Steps

### Vercel Deployment

1. **Prepare Repository**
   ```bash
   git add .
   git commit -m "Production ready deployment"
   git push origin main
   ```

2. **Configure Vercel**
   - Import repository in Vercel dashboard
   - Set environment variables:
     - `OPENROUTER_API_KEY`
     - `REDIS_URL`
     - `MODEL_NAME` (optional)

3. **Deploy**
   ```bash
   npm run deploy  # Validates and builds locally
   ```
   Or use Vercel's automatic deployment

4. **Verify Deployment**
   - Check health endpoint: `https://your-app.vercel.app/api/health`
   - Test chat functionality
   - Monitor function logs

### Alternative Platforms

#### Railway
1. Connect GitHub repository
2. Set environment variables
3. Deploy Redis addon
4. Deploy application

#### Netlify
1. Build command: `npm run build`
2. Publish directory: `.next`
3. Set environment variables
4. Configure Redis separately

## Post-Deployment

### Monitoring
- [ ] Health checks are green
- [ ] Function logs show no errors
- [ ] Redis connection is stable
- [ ] Response times are acceptable

### Testing
- [ ] End-to-end functionality test
- [ ] Load testing (if needed)
- [ ] Error scenarios testing

### Documentation
- [ ] Update README with live URL
- [ ] Document any deployment-specific configurations
- [ ] Update API documentation if needed

## Troubleshooting

### Common Issues

1. **Environment Variables Not Set**
   - Check Vercel dashboard environment variables
   - Ensure variable names match exactly
   - Redeploy after adding variables

2. **Redis Connection Issues**
   - Verify Redis URL format
   - Check Redis instance is running
   - Test connection from deployment platform

3. **Build Failures**
   - Check TypeScript errors
   - Verify all dependencies are installed
   - Check for missing environment variables during build

4. **Function Timeouts**
   - Increase `maxDuration` in `vercel.json`
   - Optimize MCP connection handling
   - Check Redis response times

### Rollback Plan
1. Revert to previous Git commit
2. Redeploy previous version
3. Check environment variables
4. Monitor for stability

## Security Considerations

- [ ] API keys are not exposed in client-side code
- [ ] Environment variables are properly secured
- [ ] CORS is configured appropriately
- [ ] Rate limiting is considered (if needed)
- [ ] Input validation is in place

## Performance Optimization

- [ ] Redis connection pooling (if applicable)
- [ ] Function cold start optimization
- [ ] Caching strategies implemented
- [ ] Bundle size optimization

## Maintenance

### Regular Tasks
- Monitor Redis usage and costs
- Update dependencies regularly
- Monitor API usage and costs
- Review and rotate API keys periodically

### Scaling Considerations
- Redis instance sizing
- Function concurrency limits
- API rate limits
- Cost monitoring
