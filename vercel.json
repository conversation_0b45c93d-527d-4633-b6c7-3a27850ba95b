{"functions": {"src/app/api/*/route.ts": {"maxDuration": 60}, "src/app/api/mcp/[transport]/route.ts": {"maxDuration": 60}}, "env": {"NODE_ENV": "production"}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}]}